import { expect } from '@esm-bundle/chai';
import { fixture, html } from '@open-wc/testing';
import '../src/welcome-component.js';

describe('WelcomeComponent', () => {
  let element;

  const mockCompany = {
    id: 1,
    name: '测试科技公司',
    address: '北京市朝阳区测试街道123号',
    contact: '张三',
    phone: '010-12345678'
  };

  beforeEach(async () => {
    element = await fixture(html`<welcome-component></welcome-component>`);
  });

  it('should render welcome title', () => {
    const title = element.shadowRoot.querySelector('.title');
    expect(title).to.exist;
    expect(title.textContent).to.equal('欢迎使用易中2.0！');
  });

  it('should show first-time user content when no existing bids', async () => {
    element.hasExistingBids = false;
    await element.updateComplete;

    const subtitle = element.shadowRoot.querySelector('.subtitle');
    expect(subtitle.textContent).to.include('您还没有创建任何标书');

    const createButton = element.shadowRoot.querySelector('md-filled-button');
    expect(createButton).to.exist;
    expect(createButton.textContent).to.include('新建标书');
  });

  it('should show returning user content when has existing bids', async () => {
    element.hasExistingBids = true;
    await element.updateComplete;

    const subtitle = element.shadowRoot.querySelector('.subtitle');
    expect(subtitle.textContent).to.include('您的标书管理平台已准备就绪');

    const buttons = element.shadowRoot.querySelectorAll('md-filled-button');
    expect(buttons.length).to.equal(2);

    const buttonTexts = Array.from(buttons).map(btn => btn.textContent);
    expect(buttonTexts.some(text => text.includes('打开标书'))).to.be.true;
    expect(buttonTexts.some(text => text.includes('新建标书'))).to.be.true;
  });

  it('should display company information when provided', async () => {
    element.currentCompany = mockCompany;
    await element.updateComplete;

    const companyInfo = element.shadowRoot.querySelector('.company-info');
    expect(companyInfo).to.exist;
    
    const companyName = element.shadowRoot.querySelector('.company-name');
    expect(companyName.textContent).to.include(mockCompany.name);
    
    const companyDetails = element.shadowRoot.querySelector('.company-details');
    expect(companyDetails.textContent).to.include(mockCompany.address);
    expect(companyDetails.textContent).to.include(mockCompany.contact);
    expect(companyDetails.textContent).to.include(mockCompany.phone);
  });

  it('should not display company information when not provided', async () => {
    element.currentCompany = null;
    await element.updateComplete;

    const companyInfo = element.shadowRoot.querySelector('.company-info');
    expect(companyInfo).to.not.exist;
  });

  it('should emit create-bid event when create button is clicked', async () => {
    let eventFired = false;
    element.addEventListener('create-bid', () => {
      eventFired = true;
    });

    const createButton = element.shadowRoot.querySelector('md-filled-button');
    createButton.click();

    expect(eventFired).to.be.true;
  });

  it('should emit open-bid event when open button is clicked', async () => {
    element.hasExistingBids = true;
    await element.updateComplete;

    let eventFired = false;
    element.addEventListener('open-bid', () => {
      eventFired = true;
    });

    const buttons = element.shadowRoot.querySelectorAll('md-filled-button');
    const openButton = Array.from(buttons).find(btn => btn.textContent.includes('打开标书'));
    expect(openButton).to.exist;
    openButton.click();

    expect(eventFired).to.be.true;
  });

  it('should emit import-bid event when import button is clicked', async () => {
    let eventFired = false;
    element.addEventListener('import-bid', () => {
      eventFired = true;
    });

    const importButton = element.shadowRoot.querySelector('md-text-button');
    importButton.click();

    expect(eventFired).to.be.true;
  });

  it('should emit switch-company event when switch company button is clicked', async () => {
    let eventFired = false;
    element.addEventListener('switch-company', () => {
      eventFired = true;
    });

    const textButtons = element.shadowRoot.querySelectorAll('md-text-button');
    const switchButton = Array.from(textButtons).find(btn => btn.textContent.includes('切换为其他公司'));
    expect(switchButton).to.exist;
    switchButton.click();

    expect(eventFired).to.be.true;
  });

  it('should show different secondary actions based on hasExistingBids', async () => {
    // Test with no existing bids
    element.hasExistingBids = false;
    await element.updateComplete;

    let textButtons = element.shadowRoot.querySelectorAll('md-text-button');
    let buttonTexts = Array.from(textButtons).map(btn => btn.textContent);
    expect(buttonTexts.some(text => text.includes('导入现有标书'))).to.be.true;
    expect(buttonTexts.some(text => text.includes('切换为其他公司'))).to.be.true;

    // Test with existing bids
    element.hasExistingBids = true;
    await element.updateComplete;

    textButtons = element.shadowRoot.querySelectorAll('md-text-button');
    buttonTexts = Array.from(textButtons).map(btn => btn.textContent);
    expect(buttonTexts.some(text => text.includes('导入标书'))).to.be.true;
    expect(buttonTexts.some(text => text.includes('切换为其他公司'))).to.be.true;
  });

  it('should be responsive on mobile devices', async () => {
    // Test that mobile styles are applied correctly
    const container = element.shadowRoot.querySelector('.container');
    expect(container).to.exist;
    
    const welcomeCard = element.shadowRoot.querySelector('.welcome-card');
    expect(welcomeCard).to.exist;
    
    const actions = element.shadowRoot.querySelector('.actions');
    expect(actions).to.exist;
  });

  it('should handle missing company information gracefully', async () => {
    element.currentCompany = {
      id: 1,
      name: '简单公司'
      // Missing address, contact, phone
    };
    await element.updateComplete;

    const companyInfo = element.shadowRoot.querySelector('.company-info');
    expect(companyInfo).to.exist;
    
    const companyName = element.shadowRoot.querySelector('.company-name');
    expect(companyName.textContent).to.include('简单公司');
    
    // Should not crash when optional fields are missing
    const companyDetails = element.shadowRoot.querySelector('.company-details');
    expect(companyDetails).to.exist;
  });
});
