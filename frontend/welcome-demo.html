<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome Component Demo</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    
    .demo-container {
      max-width: 1200px;
      margin: 0 auto;
    }
    
    .demo-section {
      margin-bottom: 40px;
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .demo-title {
      color: #1976d2;
      margin-bottom: 16px;
      font-size: 1.2rem;
      font-weight: 500;
    }
    
    .event-log {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      margin-top: 20px;
      font-family: monospace;
      font-size: 0.9rem;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .event-entry {
      margin-bottom: 8px;
      padding: 4px 8px;
      background: white;
      border-radius: 4px;
      border-left: 3px solid #1976d2;
    }
    
    .controls {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }
    
    button {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      background: #1976d2;
      color: white;
      cursor: pointer;
      font-size: 0.9rem;
    }
    
    button:hover {
      background: #1565c0;
    }
    
    button:disabled {
      background: #ccc;
      cursor: not-allowed;
    }
  </style>
</head>
<body>
  <div class="demo-container">
    <h1 style="color: white; text-align: center; margin-bottom: 40px;">Welcome Component 演示</h1>
    
    <div class="demo-section">
      <h2 class="demo-title">无现有标书的情况</h2>
      <welcome-component id="welcome1"></welcome-component>
    </div>
    
    <div class="demo-section">
      <h2 class="demo-title">有现有标书的情况</h2>
      <welcome-component id="welcome2"></welcome-component>
    </div>
    
    <div class="demo-section">
      <h2 class="demo-title">带公司信息的情况</h2>
      <welcome-component id="welcome3"></welcome-component>
    </div>
    
    <div class="demo-section">
      <h2 class="demo-title">控制面板</h2>
      <div class="controls">
        <button onclick="toggleBids()">切换标书状态</button>
        <button onclick="toggleCompany()">切换公司信息</button>
        <button onclick="clearLog()">清空日志</button>
      </div>
      
      <div class="event-log" id="eventLog">
        <div style="color: #666; font-style: italic;">事件日志将显示在这里...</div>
      </div>
    </div>
  </div>

  <script type="module">
    import './src/welcome-component.js';
    
    const mockCompany = {
      id: 1,
      name: '易中科技有限公司',
      address: '北京市朝阳区科技园区123号',
      contact: '张经理',
      phone: '010-12345678'
    };
    
    let hasExistingBids = false;
    let hasCompany = false;
    
    function setupWelcomeComponent(id, hasExistingBids, currentCompany) {
      const element = document.getElementById(id);
      element.hasExistingBids = hasExistingBids;
      element.currentCompany = currentCompany;
      
      // 添加事件监听器
      ['create-bid', 'open-bid', 'import-bid', 'switch-company'].forEach(eventName => {
        element.addEventListener(eventName, (e) => {
          logEvent(eventName, e.detail || {});
        });
      });
    }
    
    function logEvent(eventName, detail) {
      const log = document.getElementById('eventLog');
      const entry = document.createElement('div');
      entry.className = 'event-entry';
      entry.innerHTML = `
        <strong>${new Date().toLocaleTimeString()}</strong> - 
        <span style="color: #1976d2;">${eventName}</span>
        ${Object.keys(detail).length > 0 ? ` - ${JSON.stringify(detail)}` : ''}
      `;
      log.appendChild(entry);
      log.scrollTop = log.scrollHeight;
    }
    
    function updateComponents() {
      setupWelcomeComponent('welcome1', false, null);
      setupWelcomeComponent('welcome2', true, null);
      setupWelcomeComponent('welcome3', hasExistingBids, hasCompany ? mockCompany : null);
    }
    
    window.toggleBids = function() {
      hasExistingBids = !hasExistingBids;
      updateComponents();
      logEvent('system', { action: 'toggle-bids', hasExistingBids });
    };
    
    window.toggleCompany = function() {
      hasCompany = !hasCompany;
      updateComponents();
      logEvent('system', { action: 'toggle-company', hasCompany });
    };
    
    window.clearLog = function() {
      const log = document.getElementById('eventLog');
      log.innerHTML = '<div style="color: #666; font-style: italic;">事件日志已清空...</div>';
    };
    
    // 初始化
    updateComponents();
  </script>
</body>
</html>
