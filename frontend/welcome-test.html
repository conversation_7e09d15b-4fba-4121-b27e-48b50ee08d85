<!DOCTYPE html>
<html lang="zh-CN">
<head>
  <meta charset="UTF-8">
  <meta name="viewport" content="width=device-width, initial-scale=1.0">
  <title>Welcome Page Test</title>
  <style>
    body {
      margin: 0;
      padding: 20px;
      font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
      background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
      min-height: 100vh;
    }
    
    .test-container {
      max-width: 1000px;
      margin: 0 auto;
      background: white;
      border-radius: 12px;
      padding: 20px;
      box-shadow: 0 4px 12px rgba(0,0,0,0.1);
    }
    
    .event-log {
      background: #f8f9fa;
      border-radius: 8px;
      padding: 16px;
      margin-top: 20px;
      font-family: monospace;
      font-size: 0.9rem;
      max-height: 200px;
      overflow-y: auto;
    }
    
    .event-entry {
      margin-bottom: 8px;
      padding: 4px 8px;
      background: white;
      border-radius: 4px;
      border-left: 3px solid #1976d2;
    }
    
    .controls {
      display: flex;
      gap: 12px;
      margin-bottom: 20px;
      flex-wrap: wrap;
    }
    
    button {
      padding: 8px 16px;
      border: none;
      border-radius: 6px;
      background: #1976d2;
      color: white;
      cursor: pointer;
      font-size: 0.9rem;
    }
    
    button:hover {
      background: #1565c0;
    }
  </style>
</head>
<body>
  <div class="test-container">
    <h1>Welcome Page 事件测试</h1>
    
    <div class="controls">
      <button onclick="clearLog()">清空日志</button>
      <button onclick="toggleBids()">切换标书状态</button>
      <button onclick="toggleCompany()">切换公司信息</button>
    </div>
    
    <welcome-page id="welcome-test"></welcome-page>
    
    <div class="event-log" id="eventLog">
      <div style="color: #666; font-style: italic;">事件日志将显示在这里...</div>
    </div>
  </div>

  <script type="module">
    import './src/welcome-page.js';
    
    const mockCompany = {
      id: 1,
      name: '易中科技有限公司',
      address: '北京市朝阳区科技园区123号',
      contact: '张经理',
      phone: '010-12345678'
    };
    
    let hasExistingBids = false;
    let hasCompany = false;
    
    const welcomeElement = document.getElementById('welcome-test');
    
    function updateWelcomeComponent() {
      welcomeElement.hasExistingBids = hasExistingBids;
      welcomeElement.currentCompany = hasCompany ? mockCompany : null;
    }
    
    function logEvent(eventName, detail) {
      const log = document.getElementById('eventLog');
      const entry = document.createElement('div');
      entry.className = 'event-entry';
      entry.innerHTML = `
        <strong>${new Date().toLocaleTimeString()}</strong> - 
        <span style="color: #1976d2;">${eventName}</span>
        ${Object.keys(detail || {}).length > 0 ? ` - ${JSON.stringify(detail)}` : ''}
      `;
      log.appendChild(entry);
      log.scrollTop = log.scrollHeight;
    }
    
    // 添加事件监听器
    ['create-bid', 'open-bid', 'import-bid', 'switch-company'].forEach(eventName => {
      welcomeElement.addEventListener(eventName, (e) => {
        logEvent(eventName, e.detail || {});
        console.log(`事件触发: ${eventName}`, e.detail);
      });
    });
    
    window.toggleBids = function() {
      hasExistingBids = !hasExistingBids;
      updateWelcomeComponent();
      logEvent('system', { action: 'toggle-bids', hasExistingBids });
    };
    
    window.toggleCompany = function() {
      hasCompany = !hasCompany;
      updateWelcomeComponent();
      logEvent('system', { action: 'toggle-company', hasCompany });
    };
    
    window.clearLog = function() {
      const log = document.getElementById('eventLog');
      log.innerHTML = '<div style="color: #666; font-style: italic;">事件日志已清空...</div>';
    };
    
    // 初始化
    updateWelcomeComponent();
  </script>
</body>
</html>
